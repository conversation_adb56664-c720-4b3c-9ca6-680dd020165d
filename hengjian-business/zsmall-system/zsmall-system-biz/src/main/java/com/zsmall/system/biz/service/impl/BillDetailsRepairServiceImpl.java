package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.zsmall.system.biz.service.BillHeadSupper;
import com.zsmall.system.biz.service.IBillDetailsRepairService;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.dto.BillConfirmedSendErpDTO;
import com.zsmall.system.entity.domain.dto.BillRepairDifferenceDTO;
import com.zsmall.system.entity.domain.vo.billDetailsRepair.BillDetailsRepairVo;
import com.zsmall.system.entity.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 账单明细修补表服务实现
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillDetailsRepairServiceImpl implements IBillDetailsRepairService {

    private final BillDetailsRepairMapper billDetailsRepairMapper;
    private final BillDetailsMapper billDetailsMapper;
    private final BillHeadMapper billHeadMapper;
    private final BillTransactionReceiptMapper billTransactionReceiptMapper;
    private final BillHeadSupper billHeadSupper;
    private final RabbitTemplate rabbitTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void repairBillData(String billNo, String orderNo) {
        log.info("开始修补账单数据，参数：billNo={}, orderNo={}", billNo, orderNo);

        try {
            // 1. 参数校验
            if (StrUtil.isBlank(billNo)) {
                throw new RuntimeException("账单编号不能为空");
            }
            if (StrUtil.isBlank(orderNo)) {
                throw new RuntimeException("订单编号不能为空");
            }

            LambdaQueryWrapper<BillHead> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BillHead::getBillNo, billNo)
                        .eq(BillHead::getDelFlag, 0);
            BillHead billHead = TenantHelper.ignore(() -> billHeadMapper.selectOne(queryWrapper));
            if (billHead == null) {
                throw new RuntimeException("未找到指定的账单信息：" + billNo);
            }

            // 2. 解析参数
            List<String> orderNos = Arrays.asList(orderNo.split(","));
            log.info("需要修补的账单编号：{}", billNo);
            log.info("需要修补的订单号：{}", orderNos);
            // 3. 清理同一订单的旧未推送修补记录
            cleanupOldUnsentRepairRecords(orderNos, billNo);

            // 4. 备份原账单数据到修补表
            backupBillDetailsToRepairTable(orderNos, billNo);

            // 6. 删除当前账单相关数据
            deleteCurrentMonthBillData(billNo);
            String tenantId = billHead.getTenantId();
            Date billStartTime = billHead.getBillStartTime();
            Date billEndTime = billHead.getBillEndTime();
            // 格式化为字符串，用于后续的账单生成
            String startTime = DateUtil.format(billStartTime, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(billEndTime, "yyyy-MM-dd HH:mm:ss");

            // 获取开始时间所在月的第一天
            DateTime currentMonthStart = DateUtil.beginOfMonth(billStartTime);
            // 获取结束时间所在月的最后一天
            DateTime currentMonthEnd = DateUtil.endOfMonth(billEndTime);



            // 7. 重新生成当月的账单数据
            billHeadSupper.generatedDistributorBillByTenant(billHead.getTenantId(), false, null, null, startTime, endTime);

            // 8. 生成当月的账单汇总数据
            billHeadSupper.generatedTransactionReceipt(tenantId, false, null, null, startTime, endTime);

            // 8. 计算差值并准备ERP推送数据（使用最新的修补记录）
            List<BillRepairDifferenceDTO> differences = calculateBillDifferencesInternal(orderNos, tenantId);

            // 9. 如果有差值，准备推送到ERP
            if (CollectionUtil.isNotEmpty(differences)) {
                prepareBillDifferenceForErp(differences, tenantId);
            }

            log.info("账单数据修补完成，租户ID：{}", tenantId);

        } catch (Exception e) {
            log.error("账单数据修补失败，租户ID：{}，错误信息：{}", tenantId, e.getMessage(), e);
            throw new RuntimeException("账单数据修补失败：" + e.getMessage(), e);
        }
    }

    @Override
    public R<List<BillDetailsRepairVo>> queryPageList(String tenantId, String orderNo, PageQuery pageQuery) {
        LambdaQueryWrapper<BillDetailsRepair> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillDetailsRepair::getDelFlag, 0);

        if (StrUtil.isNotBlank(tenantId)) {
            queryWrapper.eq(BillDetailsRepair::getTenantId, tenantId);
        }

        if (StrUtil.isNotBlank(orderNo)) {
            queryWrapper.like(BillDetailsRepair::getOrderNo, orderNo);
        }

        queryWrapper.orderByDesc(BillDetailsRepair::getCreateTime);

        Page<BillDetailsRepair> page = billDetailsRepairMapper.selectPage(pageQuery.build(), queryWrapper);
        List<BillDetailsRepairVo> voList = BeanUtil.copyToList(page.getRecords(), BillDetailsRepairVo.class);

        return R.ok(voList, TableDataInfo.build(page));
    }

    @Override
    public R<List<BillDetailsRepairVo>> getByOrderNos(String orderNos) {
        if (StrUtil.isBlank(orderNos)) {
            return R.ok(new ArrayList<>());
        }

        List<String> orderNoList = Arrays.asList(orderNos.split(","));
        List<BillDetailsRepair> repairList = TenantHelper.ignore(() ->
            billDetailsRepairMapper.selectByOrderNos(orderNoList));

        List<BillDetailsRepairVo> voList = BeanUtil.copyToList(repairList, BillDetailsRepairVo.class);
        return R.ok(voList);
    }

    @Override
    public R<List<BillDetailsRepairVo>> getByTenantAndTimeRange(String tenantId, String startTime, String endTime) {
        List<BillDetailsRepair> repairList = TenantHelper.ignore(() ->
            billDetailsRepairMapper.selectByTenantIdAndTimeRange(tenantId, startTime, endTime));

        List<BillDetailsRepairVo> voList = BeanUtil.copyToList(repairList, BillDetailsRepairVo.class);
        return R.ok(voList);
    }

    @Override
    public R<List<BillRepairDifferenceDTO>> calculateBillDifferences(String tenantId, String orderNos) {
        if (StrUtil.isBlank(orderNos)) {
            return R.ok(new ArrayList<>());
        }

        List<String> orderNoList = Arrays.asList(orderNos.split(","));
        List<BillRepairDifferenceDTO> differences = calculateBillDifferencesInternal(orderNoList, tenantId);

        return R.ok(differences);
    }

    @Override
    public void resendDifferenceToErp(String tenantId, String orderNos) {
        log.info("重新推送差值数据到ERP，租户ID：{}, 订单号：{}", tenantId, orderNos);

        try {
            List<String> orderNoList = Arrays.asList(orderNos.split(","));
            List<BillRepairDifferenceDTO> differences = calculateBillDifferencesInternal(orderNoList, tenantId);

            if (CollectionUtil.isNotEmpty(differences)) {
                prepareBillDifferenceForErp(differences, tenantId);
                log.info("差值数据重新推送完成，租户ID：{}", tenantId);
            } else {
                log.warn("未找到差值数据，租户ID：{}", tenantId);
            }

        } catch (Exception e) {
            log.error("重新推送差值数据到ERP失败，租户ID：{}，错误信息：{}", tenantId, e.getMessage(), e);
            throw new RuntimeException("重新推送差值数据到ERP失败：" + e.getMessage(), e);
        }
    }

    @Override
    public void pushUnsentRepairDataToErp() {
        log.info("开始推送修补表中未推送的全量数据到ERP");

        try {
            // 查询所有未推送的修补数据
            List<BillDetailsRepair> unsentData = TenantHelper.ignore(() ->
                billDetailsRepairMapper.selectUnsentRepairData());

            if (CollectionUtil.isEmpty(unsentData)) {
                log.info("没有未推送的修补数据");
                return;
            }

            log.info("找到{}条未推送的修补数据", unsentData.size());

            // 按租户分组
            Map<String, List<BillDetailsRepair>> tenantGroupMap = unsentData.stream()
                .collect(Collectors.groupBy(BillDetailsRepair::getTenantId));

            // 逐个租户推送
            for (Map.Entry<String, List<BillDetailsRepair>> entry : tenantGroupMap.entrySet()) {
                String tenantId = entry.getKey();
                List<BillDetailsRepair> tenantRepairData = entry.getValue();

                try {
                    // 转换为差值计算DTO
                    List<BillRepairDifferenceDTO> differences = convertRepairDataToDifferences(tenantRepairData);

                    if (CollectionUtil.isNotEmpty(differences)) {
                        // 推送到ERP
                        prepareBillDifferenceForErp(differences, tenantId);

                        // 更新推送状态
                        List<Long> ids = tenantRepairData.stream()
                            .map(BillDetailsRepair::getId)
                            .collect(Collectors.toList());

                        TenantHelper.ignore(() -> {
                            billDetailsRepairMapper.batchUpdatePushStatus(ids, 1);
                            return null;
                        });

                        log.info("租户{}的{}条修补数据推送完成", tenantId, tenantRepairData.size());
                    }

                } catch (Exception e) {
                    log.error("推送租户{}的修补数据失败：{}", tenantId, e.getMessage(), e);
                    // 继续处理其他租户的数据
                }
            }

            log.info("修补表未推送数据推送任务执行完成");

        } catch (Exception e) {
            log.error("推送修补表未推送数据失败：{}", e.getMessage(), e);
            throw new RuntimeException("推送修补表未推送数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 备份原账单数据到修补表
     */
    private void backupBillDetailsToRepairTable(List<String> orderNos, String billNo) {

        log.info("开始备份账单数据到修补表，租户ID：{}, 订单数量：{}", billNo, orderNos.size());

        // 查询需要备份的账单明细数据
        LambdaQueryWrapper<BillDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillDetails::getBillNo, billNo)
                    .in(BillDetails::getOrderNo, orderNos)
                   .eq(BillDetails::getDelFlag, 0);

        List<BillDetails> billDetailsList = TenantHelper.ignore(() -> billDetailsMapper.selectList(queryWrapper));

        if (CollectionUtil.isEmpty(billDetailsList)) {
            log.warn("未找到需要备份的账单数据， 账单编号：{}", billNo);
            return;
        }

        // 转换为修补表数据
        List<BillDetailsRepair> repairList = new ArrayList<>();
        for (BillDetails billDetails : billDetailsList) {
            BillDetailsRepair repair = new BillDetailsRepair();
            // 复制属性时忽略ERP推送相关字段，因为这两个字段的维度不同
            BeanUtil.copyProperties(billDetails, repair, "isSendErp", "sendErpTime");
            repair.setId(IdUtil.getSnowflakeNextId());
            repair.setIsSendErp(0);
            repair.setSendErpTime(null);
            repairList.add(repair);
        }

        // 批量插入修补表
        TenantHelper.ignore(() -> {
            billDetailsRepairMapper.batchInsertBillDetailsRepair(repairList);
            return null;
        });

        log.info("成功备份{}条账单数据到修补表", repairList.size());
    }

    /**
     * 清理同一账单下同一订单的旧未推送修补记录
     * 在新修补操作前，删除相同账单编号下相同订单的所有未推送修补记录，避免重复推送
     * 注意：不同账单编号下的相同订单号应该独立处理
     */
    private void cleanupOldUnsentRepairRecords(List<String> orderNos, String billNo) {
        billDetailsRepairMapper.delete(new LambdaQueryWrapper<BillDetailsRepair>()
                .eq(BillDetailsRepair::getBillNo, billNo)
                .in(BillDetailsRepair::getOrderNo, orderNos)
                .eq(BillDetailsRepair::getIsSendErp, 0));
        log.info("旧修补记录清理完成");
    }
    /**
     * 从billHead表查询账单的时间范围
     * @param billNo 账单编号
     * @param tenantId 租户ID
     * @return 时间范围数组 [开始时间, 结束时间]
     */
    private String[] getBillTimeRangeFromBillHead(String billNo, String tenantId) {
        log.info("从billHead表查询账单时间范围，账单编号：{}, 租户ID：{}", billNo, tenantId);

        // 查询指定账单的时间范围
        LambdaQueryWrapper<BillHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillHead::getBillNo, billNo)
                   .eq(BillHead::getTenantId, tenantId)
                   .eq(BillHead::getDelFlag, 0)
                   .select(BillHead::getBillStartTime, BillHead::getBillEndTime);

        BillHead billHead = TenantHelper.ignore(() -> billHeadMapper.selectOne(queryWrapper));

        if (billHead == null) {
            log.warn("未找到账单信息，账单编号：{}, 租户ID：{}", billNo, tenantId);
            throw new RuntimeException("未找到指定的账单信息：" + billNo);
        }

        String startTime = DateUtil.format(billHead.getBillStartTime(), "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(billHead.getBillEndTime(), "yyyy-MM-dd HH:mm:ss");

        log.info("查询到账单时间范围：{} 到 {}", startTime, endTime);
        return new String[]{startTime, endTime};
    }

    /**
     * 删除指定账单的数据
     */
    private void deleteCurrentMonthBillData(String billNo) {
        log.info("开始删除指定账单数据，账单编号：{}", billNo);

        // 删除账单明细数据
        LambdaQueryWrapper<BillDetails> billDetailsQuery = new LambdaQueryWrapper<>();
        billDetailsQuery.eq(BillDetails::getBillNo, billNo)
                       .eq(BillDetails::getDelFlag, 0);

        int deletedDetails = TenantHelper.ignore(() -> billDetailsMapper.delete(billDetailsQuery));
        log.info("删除账单明细数据{}条", deletedDetails);

        // 删除账单头数据
        LambdaQueryWrapper<BillHead> billHeadQuery = new LambdaQueryWrapper<>();
        billHeadQuery.eq(BillHead::getBillNo, billNo)
                    .eq(BillHead::getDelFlag, 0);

        int deletedHeads = TenantHelper.ignore(() -> billHeadMapper.delete(billHeadQuery));
        log.info("删除账单头数据{}条", deletedHeads);

        // 删除账单汇总数据（根据账单编号关联删除）
        // 注意：这里需要根据实际的业务逻辑来确定如何关联删除汇总数据
        // 如果bill_abstract_detail表中有billNo字段，则可以直接删除
        // 如果没有，可能需要通过其他方式关联删除
        LambdaQueryWrapper<BillTransactionReceipt> abstractQuery = new LambdaQueryWrapper<>();
        abstractQuery.eq(BillTransactionReceipt::getBillNo, billNo).eq(BillTransactionReceipt::getDelFlag, 0);
        int deletedAbstracts = TenantHelper.ignore(() -> billTransactionReceiptMapper.delete(abstractQuery));
        log.info("删除账单汇总数据{}条", deletedAbstracts);
    }

    /**
     * 计算账单修补前后的差值（内部方法）
     */
    private List<BillRepairDifferenceDTO> calculateBillDifferencesInternal(List<String> orderNos, String tenantId) {
        log.info("开始计算账单差值，租户ID：{}, 订单数量：{}", tenantId, orderNos.size());

        if (CollectionUtil.isEmpty(orderNos)) {
            return new ArrayList<>();
        }

        // 查询修补表中的最新数据（修补前）
        List<BillDetailsRepair> repairData = TenantHelper.ignore(() ->
            billDetailsRepairMapper.selectLatestByOrderNos(orderNos));

        // 查询当前账单表中的数据（修补后）
        LambdaQueryWrapper<BillDetails> currentQuery = new LambdaQueryWrapper<>();
        currentQuery.eq(BillDetails::getTenantId, tenantId)
                   .in(BillDetails::getOrderNo, orderNos)
                   .eq(BillDetails::getDelFlag, 0);

        List<BillDetails> currentData = TenantHelper.ignore(() ->
            billDetailsMapper.selectList(currentQuery));

        // 按订单号分组
        Map<String, BillDetailsRepair> repairMap = repairData.stream()
            .collect(Collectors.toMap(BillDetailsRepair::getOrderNo, Function.identity(), (v1, v2) -> v1));

        Map<String, BillDetails> currentMap = currentData.stream()
            .collect(Collectors.toMap(BillDetails::getOrderNo, Function.identity(), (v1, v2) -> v1));

        // 计算差值
        List<BillRepairDifferenceDTO> differences = new ArrayList<>();

        for (String orderNo : orderNos) {
            BillDetailsRepair beforeData = repairMap.get(orderNo);
            BillDetails afterData = currentMap.get(orderNo);

            if (beforeData != null && afterData != null) {
                BillRepairDifferenceDTO difference = createDifferenceDTO(beforeData, afterData);
                difference.calculateDifferences();

                // 只保存有差值的记录
                if (difference.hasDifference()) {
                    differences.add(difference);
                }
            }
        }

        log.info("计算完成，发现{}条有差值的记录", differences.size());
        return differences;
    }
    /**
     * 创建差值计算DTO
     */
    private BillRepairDifferenceDTO createDifferenceDTO(BillDetailsRepair beforeData, BillDetails afterData) {
        BillRepairDifferenceDTO dto = new BillRepairDifferenceDTO();

        dto.setOrderNo(beforeData.getOrderNo());
        dto.setTenantId(beforeData.getTenantId());
        dto.setSupperTenantId(beforeData.getSupperTenantId());
        dto.setProductSkuCode(beforeData.getProductSkuCode());
        dto.setCurrencyCode(beforeData.getCurrencyCode());
        dto.setCurrencySymbol(beforeData.getCurrencySymbol());
        dto.setCountryCode(beforeData.getCountryCode());

        // 修补前数据
        dto.setBeforeOperationFee(beforeData.getOperationFee());
        dto.setBeforeFinalDeliveryFee(beforeData.getFinalDeliveryFee());
        dto.setBeforeProductSkuPrice(beforeData.getProductSkuPrice());
        dto.setBeforeOrderTotalAmount(beforeData.getOrderTotalAmount());
        dto.setBeforeOrderRefundTotalAmount(beforeData.getOrderRefundTotalAmount());

        // 修补后数据
        dto.setAfterOperationFee(afterData.getOperationFee());
        dto.setAfterFinalDeliveryFee(afterData.getFinalDeliveryFee());
        dto.setAfterProductSkuPrice(afterData.getProductSkuPrice());
        dto.setAfterOrderTotalAmount(afterData.getOrderTotalAmount());
        dto.setAfterOrderRefundTotalAmount(afterData.getOrderRefundTotalAmount());

        return dto;
    }

    /**
     * 准备差值数据推送到ERP
     */
    private void prepareBillDifferenceForErp(List<BillRepairDifferenceDTO> differences, String tenantId) {
        log.info("准备推送差值数据到ERP，租户ID：{}, 差值记录数：{}", tenantId, differences.size());

        try {
            // 按差值记录逐个构建ERP推送数据
            for (int i = 0; i < differences.size(); i++) {
                BillRepairDifferenceDTO difference = differences.get(i);

                // 构建ERP推送数据，使用现有的BillConfirmedSendErpDTO结构
                BillConfirmedSendErpDTO erpData = new BillConfirmedSendErpDTO();
                erpData.setChannelName("Distribution_Repair"); // 标识为修补数据
                erpData.setBillNo("REPAIR_" + difference.getOrderNo() + "_" + System.currentTimeMillis());
                erpData.setBillConfirmedTime(new Date());
                erpData.setCurrencyCode(difference.getCurrencyCode());
                erpData.setTotal(difference.getOrderTotalAmountDifference());
                erpData.setIsLastData(i == differences.size() - 1);

                // 构建账单明细
                BillConfirmedSendErpDTO.BillDetailsConfirmedSendErpDTO billDetails =
                    new BillConfirmedSendErpDTO.BillDetailsConfirmedSendErpDTO();

                billDetails.setSupplierTenantId(difference.getSupperTenantId());
                billDetails.setBillConfirmedTime(new Date());
                billDetails.setBillNo(erpData.getBillNo());
                billDetails.setOrderNo(difference.getOrderNo());
                billDetails.setOrderStatus(1); // 修补数据标识为发货单
                billDetails.setCurrencyCode(difference.getCurrencyCode());

                // 设置差值数据（转换为字符串格式）
                billDetails.setProductSkuPrice(difference.getProductSkuPriceDifference().toString());
                billDetails.setOperationFee(difference.getOperationFeeDifference().toString());
                billDetails.setFinalDeliveryFee(difference.getFinalDeliveryFeeDifference().toString());
                billDetails.setOrderTotalAmount(difference.getOrderTotalAmountDifference());
                billDetails.setOrderRefundTotalAmount(difference.getOrderRefundTotalAmountDifference());

                erpData.setBillDetailsConfirmedSendErpDTOS(billDetails);

                // 发送到MQ队列，异步推送到ERP
                rabbitTemplate.convertAndSend(
                    RabbitMqConstant.BILL_REPAIR_QUEUE,
                    JSONUtil.toJsonStr(erpData)
                );
            }

            log.info("差值数据已发送到MQ队列，等待推送到ERP，共{}条记录", differences.size());

        } catch (Exception e) {
            log.error("准备ERP推送数据失败，租户ID：{}，错误信息：{}", tenantId, e.getMessage(), e);
            // 这里不抛异常，避免影响主流程
        }
    }

    /**
     * 转换修补数据为差值DTO（用于定时任务推送）
     */
    private List<BillRepairDifferenceDTO> convertRepairDataToDifferences(List<BillDetailsRepair> repairDataList) {
        List<BillRepairDifferenceDTO> differences = new ArrayList<>();

        // 按订单号分组，获取每个订单的修补前数据
        Map<String, BillDetailsRepair> repairMap = repairDataList.stream()
            .collect(Collectors.toMap(BillDetailsRepair::getOrderNo, Function.identity(), (v1, v2) ->
                v1.getCreateTime().after(v2.getCreateTime()) ? v1 : v2)); // 保留最新的记录

        for (Map.Entry<String, BillDetailsRepair> entry : repairMap.entrySet()) {
            String orderNo = entry.getKey();
            BillDetailsRepair repairData = entry.getValue();

            // 查询当前账单表中的数据（修补后）
            LambdaQueryWrapper<BillDetails> currentQuery = new LambdaQueryWrapper<>();
            currentQuery.eq(BillDetails::getTenantId, repairData.getTenantId())
                       .eq(BillDetails::getOrderNo, orderNo)
                       .eq(BillDetails::getDelFlag, 0);

            List<BillDetails> currentDataList = TenantHelper.ignore(() ->
                billDetailsMapper.selectList(currentQuery));

            if (CollectionUtil.isNotEmpty(currentDataList)) {
                BillDetails currentData = currentDataList.get(0); // 取第一条记录

                BillRepairDifferenceDTO difference = createDifferenceDTO(repairData, currentData);
                difference.calculateDifferences();

                // 只保存有差值的记录
                if (difference.hasDifference()) {
                    differences.add(difference);
                }
            }
        }

        return differences;
    }
}
